'use client';
import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { FaChalkboardTeacher, FaUsers, FaBookOpen, FaStar, FaGraduationCap, FaCalendarAlt, FaMapMarkerAlt, FaEnvelope, FaPhone, FaArrowLeft, FaPlay, FaClock, FaAward } from 'react-icons/fa';
import NavBar from '../../../../components/navBar';

const InstructorProfile = () => {
  const params = useParams();
  const router = useRouter();
  const [instructor, setInstructor] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // بيانات تجريبية للمدرسين
  const mockInstructors = {
    'INST001': {
      id: 'INST001',
      name: 'د. أحمد محمد علي',
      title: 'دكتور في الرياضيات',
      avatar: '/instructor-avatar.jpg',
      email: '<EMAIL>',
      phone: '+20 ************',
      location: 'القاهرة، مصر',
      joinDate: '2020-01-15',
      rating: 4.8,
      totalStudents: 1250,
      totalCourses: 15,
      totalHours: 320,
      bio: 'دكتور في الرياضيات مع أكثر من 15 عاماً من الخبرة في التدريس. متخصص في الجبر والهندسة التحليلية. حاصل على دكتوراه من جامعة القاهرة.',
      specializations: ['الجبر', 'الهندسة التحليلية', 'التفاضل والتكامل', 'الإحصاء'],
      education: [
        {
          degree: 'دكتوراه في الرياضيات',
          university: 'جامعة القاهرة',
          year: '2010'
        },
        {
          degree: 'ماجستير في الرياضيات التطبيقية',
          university: 'جامعة عين شمس',
          year: '2006'
        }
      ],
      courses: [
        {
          id: 1,
          title: 'الرياضيات - الصف الثالث الثانوي',
          students: 450,
          rating: 4.9,
          duration: '40 ساعة',
          price: '500 جنيه'
        },
        {
          id: 2,
          title: 'الجبر المتقدم',
          students: 320,
          rating: 4.7,
          duration: '35 ساعة',
          price: '400 جنيه'
        },
        {
          id: 3,
          title: 'التفاضل والتكامل',
          students: 280,
          rating: 4.8,
          duration: '45 ساعة',
          price: '600 جنيه'
        }
      ],
      achievements: [
        'أفضل مدرس للعام 2023',
        'جائزة التميز في التدريس',
        'شهادة تقدير من وزارة التربية والتعليم'
      ]
    },
    'INST002': {
      id: 'INST002',
      name: 'أ. سارة محمود حسن',
      title: 'أستاذة الفيزياء',
      avatar: '/instructor-avatar-2.jpg',
      email: '<EMAIL>',
      phone: '+20 ************',
      location: 'الإسكندرية، مصر',
      joinDate: '2019-09-10',
      rating: 4.9,
      totalStudents: 980,
      totalCourses: 12,
      totalHours: 280,
      bio: 'أستاذة فيزياء متميزة مع خبرة 12 عاماً في التدريس. متخصصة في الفيزياء النووية والكهرومغناطيسية.',
      specializations: ['الفيزياء النووية', 'الكهرومغناطيسية', 'الميكانيكا', 'البصريات'],
      education: [
        {
          degree: 'ماجستير في الفيزياء النووية',
          university: 'جامعة الإسكندرية',
          year: '2015'
        }
      ],
      courses: [
        {
          id: 4,
          title: 'الفيزياء - الصف الثالث الثانوي',
          students: 380,
          rating: 4.9,
          duration: '38 ساعة',
          price: '450 جنيه'
        }
      ],
      achievements: [
        'أفضل مدرسة للعام 2022',
        'جائزة الإبداع في التدريس'
      ]
    }
  };

  useEffect(() => {
    const fetchInstructor = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // محاكاة استدعاء API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const instructorData = mockInstructors[params.id];
        
        if (instructorData) {
          setInstructor(instructorData);
        } else {
          setError('لم يتم العثور على المدرس');
        }
      } catch (err) {
        setError('حدث خطأ أثناء تحميل بيانات المدرس');
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchInstructor();
    }
  }, [params.id]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-main flexCenter">
        <div className="flexCenter flex-col">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
          <p className="mt-4 regular-16 text-gray-600">جاري تحميل بيانات المدرس...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-main flexCenter">
        <div className="text-center">
          <FaChalkboardTeacher className="w-24 h-24 text-gray-300 mx-auto mb-4" />
          <h2 className="bold-24 text-gray-600 mb-4">{error}</h2>
          <button
            onClick={() => router.push('/instructors')}
            className="bg-accent text-white px-6 py-3 rounded-lg bold-16 hover:bg-opacity-90 transition-colors flexCenter gap-2"
          >
            <FaArrowLeft className="w-4 h-4" />
            العودة للبحث
          </button>
        </div>
      </div>
    );
  }

  if (!instructor) {
    return null;
  }

  return (
    <div className="min-h-screen bg-main">
      <NavBar />
      <div className="max-container py-8 padding-container">
        {/* Back Button */}
        <button
          onClick={() => router.push('/instructors')}
          className="flexCenter hover:bg-[#088395] hover:border hover:border-[#088395] hover:text-white gap-2 cursor-pointer text-accent hover:text-opacity-80 transition-all border border-accent rounded-lg py-1 px-4 mb-6"
        >
          <FaArrowLeft className="w-4 h-4" />
          <span className="regular-16">العودة للبحث</span>
        </button>

        {/* Instructor Header */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8">
            {/* Avatar */}
            <div className="relative">
              <div className="w-32 h-32 bg-accent rounded-full flexCenter">
                <FaChalkboardTeacher className="w-16 h-16 text-white" />
              </div>
              <div className="absolute -bottom-2 -right-2 bg-yellow-400 rounded-full p-2">
                <FaStar className="w-4 h-4 text-white" />
              </div>
            </div>

            {/* Info */}
            <div className="flex-1 text-center lg:text-right">
              <h1 className="bold-32 text-gray-900 mb-5">{instructor.name}</h1>
              <p className="regular-16 text-gray-600 mb-6 max-w-2xl">{instructor.bio}</p>
              
              {/* Stats */}
              <div className="grid grid-cols-2">
                <div className="text-center">
                  <div className="bold-20 text-accent">{instructor.totalStudents}</div>
                  <div className="regular-14 text-gray-600">طالب</div>
                </div>
                <div className="text-center">
                  <div className="bold-20 text-accent">{instructor.totalCourses}</div>
                  <div className="regular-14 text-gray-600">كورس</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* Courses */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="bold-24 text-gray-900 mb-6 flexCenter gap-2">
                <FaBookOpen className="w-6 h-6 text-accent" />
                الكورسات المتاحة
              </h2>
              
              <div className="space-y-4">
                {instructor.courses.map((course) => (
                  <div key={course.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flexBetween mb-2">
                      <h3 className="bold-18 text-gray-900">{course.title}</h3>
                      <span className="bold-16 text-accent">{course.price}</span>
                    </div>
                    
                    <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                      <div className="flexCenter gap-1">
                        <FaUsers className="w-4 h-4" />
                        <span>{course.students} طالب</span>
                      </div>
                      <div className="flexCenter gap-1">
                        <FaClock className="w-4 h-4" />
                        <span>{course.duration}</span>
                      </div>
                      <div className="flexCenter gap-1">
                        <FaStar className="w-4 h-4 text-yellow-500" />
                        <span>{course.rating}</span>
                      </div>
                    </div>
                    
                    <button className="bg-accent text-white px-4 py-2 rounded-lg regular-14 hover:bg-opacity-90 transition-colors flexCenter gap-2">
                      <FaPlay className="w-3 h-3" />
                      عرض التفاصيل
                    </button>
                  </div>
                ))}
              </div>
            </div>


          </div>

          {/* Right Column */}
          <div className="space-y-8">
            {/* Contact Info */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="bold-20 text-gray-900 mb-6">معلومات التواصل</h2>
              
              <div className="space-y-4">
                <div className="flex items-center gap-5">
                  <FaEnvelope className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-600">البريد الإلكتروني</p>
                    <p className="bold-14 text-gray-900">{instructor.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-5">
                  <FaPhone className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-600">الهاتف</p>
                    <p className="bold-14 text-gray-900">{instructor.phone}</p>
                  </div>
                </div>
              </div>
            </div>
                        {/* Specializations */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="bold-24 text-gray-900 mb-6">التخصصات</h2>
              <div className="flex flex-wrap gap-3">
                {instructor.specializations.map((spec, index) => (
                  <span key={index} className="bg-secondary bg-opacity-20 text-white px-4 py-2 rounded-full regular-14">
                    {spec}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstructorProfile;
