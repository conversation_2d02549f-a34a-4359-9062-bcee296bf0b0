'use client';
import React, { useState } from 'react';
import { FaUser, FaEnvelope, FaPhone, FaCalendarAlt, FaMapMarkerAlt, FaIdCard, FaUserGraduate, FaBook, FaClock, FaEdit } from 'react-icons/fa';
import { useUserData } from '../../../models/UserContext';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FiAward } from 'react-icons/fi';
import NavBar from '../../../components/navBar';

const Profile = () => {
  const { user } = useUserData();
  const router = useRouter()

  console.log(user)

  const [userCourses, setUserCourses] = useState([
    {
      id: 1,
      title: "الرياضيات - الصف الثالث الثانوي",
      instructor: "أ. محمد أحمد",
      progress: 75,
      totalLessons: 24,
      completedLessons: 18,
      enrolledDate: "2025-01-15",
      status: "active",
      thumbnail: "/course1.jpg"
    },
    {
      id: 2,
      title: "الفيزياء - الصف الثالث الثانوي",
      instructor: "د. سارة محمود",
      progress: 45,
      totalLessons: 20,
      completedLessons: 9,
      enrolledDate: "2025-02-01",
      status: "active",
      thumbnail: "/course2.jpg"
    },
    {
      id: 3,
      title: "الكيمياء - الصف الثالث الثانوي",
      instructor: "أ. أحمد علي",
      progress: 100,
      totalLessons: 16,
      completedLessons: 16,
      enrolledDate: "2024-12-10",
      status: "completed",
      thumbnail: "/course3.jpg"
    },
    {
      id: 4,
      title: "الأحياء - الصف الثالث الثانوي",
      instructor: "د. فاطمة حسن",
      progress: 30,
      totalLessons: 18,
      completedLessons: 5,
      enrolledDate: "2025-02-15",
      status: "active",
      thumbnail: "/course4.jpg"
    },
    {
      id: 5,
      title: "اللغة العربية - الصف الثالث الثانوي",
      instructor: "أ. محمود سالم",
      progress: 85,
      totalLessons: 22,
      completedLessons: 19,
      enrolledDate: "2024-11-20",
      status: "active",
      thumbnail: "/course5.jpg"
    },
    {
      id: 6,
      title: "التاريخ - الصف الثالث الثانوي",
      instructor: "د. نادية عبدالله",
      progress: 60,
      totalLessons: 14,
      completedLessons: 8,
      enrolledDate: "2025-01-05",
      status: "active",
      thumbnail: "/course6.jpg"
    },
  ]);

  const [showAllCourses, setShowAllCourses] = useState(false);
  const [coursesToShow, setCoursesToShow] = useState(3);

  // التحقق من حالة التحميل بناءً على وجود بيانات المستخدم
  const isLoading = !user;

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getProgressColor = (progress) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { text: 'نشط', color: 'bg-green-100 text-green-800' },
      completed: { text: 'مكتمل', color: 'bg-blue-100 text-blue-800' },
      paused: { text: 'متوقف', color: 'bg-yellow-100 text-yellow-800' }
    };

    return statusConfig[status] || statusConfig.active;
  };

  const handleShowMore = () => {
    setShowAllCourses(true);
    setCoursesToShow(userCourses.length);
  };

  const handleShowLess = () => {
    setShowAllCourses(false);
    setCoursesToShow(3);
  };

  // الحصول على الكورسات المراد عرضها
  const displayedCourses = showAllCourses ? userCourses : userCourses.slice(0, coursesToShow);

  // دالة للحصول على تفاصيل الدور
  const getRoleDetails = (role) => {
    const roleConfig = {
      STUDENT: {
        text: 'طالب',
        color: 'bg-blue-100 text-blue-800',
        icon: FaUserGraduate,
        description: 'يمكنك الوصول للكورسات والدروس'
      },
      INSTRUCTOR: {
        text: 'مدرس',
        color: 'bg-green-100 text-green-800',
        icon: FaUser,
        description: 'يمكنك إنشاء وإدارة الكورسات'
      },
      ASSISTANT: {
        text: 'مساعد',
        color: 'bg-yellow-100 text-yellow-800',
        icon: FaUser,
        description: 'يمكنك مساعدة المدرسين والطلاب'
      },
      ADMIN: {
        text: 'مدير',
        color: 'bg-red-100 text-red-800',
        icon: FaUser,
        description: 'لديك صلاحيات كاملة على النظام'
      }
    };

    return roleConfig[role] || roleConfig.STUDENT;
  };

  // دالة للحصول على عنوان قسم الكورسات حسب الدور
  const getCourseSectionTitle = (role) => {
    const titles = {
      STUDENT: 'الحصص المشتراة',
      INSTRUCTOR: 'الكورسات التي أدرسها',
      ASSISTANT: 'الكورسات المساعدة',
      ADMIN: 'جميع الكورسات'
    };

    return titles[role] || titles.STUDENT;
  };

  // دالة للحصول على تسميات الإحصائيات حسب الدور
  const getStatsLabels = (role) => {
    const labels = {
      STUDENT: {
        total: 'إجمالي الكورسات',
        completed: 'كورسات مكتملة',
        lessons: 'دروس مكتملة'
      },
      INSTRUCTOR: {
        total: 'الكورسات التي أدرسها',
        completed: 'كورسات منتهية',
        lessons: 'دروس منشورة'
      },
      ASSISTANT: {
        total: 'كورسات مساعدة',
        completed: 'كورسات منتهية',
        lessons: 'دروس مساعدة'
      },
      ADMIN: {
        total: 'إجمالي الكورسات',
        completed: 'كورسات مكتملة',
        lessons: 'إجمالي الدروس'
      }
    };

    return labels[role] || labels.STUDENT;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-main flexCenter">
        <div className="flexCenter flex-col">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
          <p className="mt-4 regular-16 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push('/login');
  }


  return (
    <div className="min-h-screen bg-main">
      <NavBar />
      <div className="max-container padding-container py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="md:items-center md:justify-between gap-4 md:gap-0 flex-col md:flex-row flex">
            <div className="md:justify-between md:items-center flex flex-col md:flex-row gap-4 w-full">
              <div className='flex flex-col justify-center text-center md:text-right md:justify- md:flex-row items-center space-x-4 md:gap-3 gap-2 space-x-reverse'>
                <div className="relative">
                  <Image
                    src={user.avatarUrl || 'https://images.unsplash.com/photo-1550399105-c4db5fb85c18?q=80&w=1171&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'}
                    width={80}
                    height={80}
                    alt="صورة المستخدم"
                    className="w-20 h-20 rounded-full object-cover border-4 border-secondary"
                    onError={(e) => {
                      e.target.src = "/default-avatar.png";
                    }}
                  />
                  <div className="absolute -bottom-1 -right-1 bg-accent text-white rounded-full p-1">
                    <FaUser className="w-3 h-3" />
                  </div>
                </div>
                <div>
                  <h1 className="bold-32 text-gray-900">{user?.fullname}</h1>
                  <p className="regular-16 text-gray-600">@{user?.username}</p>
                </div>
              </div>
                <div className="flex items-center flex-col space-x-2 space-x-reverse gap-3">
                  <span className={`inline-block px-10 py-1 rounded-full bold-16  ${getRoleDetails(user?.role).color}`}>
                    {getRoleDetails(user?.role).text}
                  </span>
                  <p className="regular-12 text-center md:text-right text-gray-500">{getRoleDetails(user?.role).description}</p>
                </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* معلومات المستخدم */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="bold-20 text-gray-900 mb-6">المعلومات الشخصية</h2>

              <div className="space-y-4">
                <div className="flex items-center space-x-3 gap-5 space-x-reverse">
                  <FaEnvelope className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-600">البريد الإلكتروني</p>
                    <p className="bold-14 text-gray-900">{user?.email|| 'لا يوجد'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 gap-5 space-x-reverse">
                  <FaPhone className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-600">رقم الهاتف</p>
                    <p className="bold-14 text-gray-900">{user?.phoneNumber|| 'لا يوجد'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 gap-5 space-x-reverse">
                  <FaPhone className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-600">هاتف ولي الأمر</p>
                    <p className="bold-14 text-gray-900">{user?.parentPhoneNumber || 'لا يوجد'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 gap-5 space-x-reverse">
                  <FaIdCard className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-600">الرقم القومي</p>
                    <p className="bold-14 text-gray-900">{user?.nationalId|| 'لا يوجد'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 gap-5 space-x-reverse">
                  <FaCalendarAlt className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-600">تاريخ الميلاد</p>
                    <p className="bold-14 text-gray-900">{formatDate(user?.dateOfBirth)|| 'لا يوجد'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 gap-5 space-x-reverse">
                  <FaMapMarkerAlt className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-600">المحافظة</p>
                    <p className="bold-14 text-gray-900">{user?.government|| 'لا يوجد'}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 gap-5 space-x-reverse">
                  <FaClock className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-600">تاريخ التسجيل</p>
                    <p className="bold-14 text-gray-900">{formatDate(user?.createdAt)|| 'لا يوجد'}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* الحصص والكورسات */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flexBetween mb-6">
                <h2 className="bold-20 text-gray-900">{getCourseSectionTitle(user?.role)}</h2>
                <div className="flex flex-col md:flex-row gap-2 items-center space-x-2 space-x-reverse">
                  <FaBook className="w-5 h-5 text-accent" />
                  <span className="regular-14 text-gray-600">{userCourses.length} كورس</span>
                </div>
              </div>

              {userCourses.length === 0 ? (
                <div className="text-center py-12">
                  <FaUserGraduate className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="bold-18 text-gray-600 mb-2">{getEmptyCoursesMessage(user?.role).title}</h3>
                  <p className="regular-14 text-gray-500">{getEmptyCoursesMessage(user?.role).description}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {displayedCourses.map((course) => {
                    const statusBadge = getStatusBadge(course.status);
                    return (
                      <div key={course.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start md:items-center md:flex-row flex-col md:gap-4 space-x-4 space-x-reverse">
                          <div className="w-16 h-16 bg-secondary mx-auto md:mx-0 rounded-lg flexCenter">
                            <FaBook className="w-8 h-8 text-white" />
                          </div>

                          <div className="flex-1">
                            <div className="flexBetween mb-2">
                              <h3 className="bold-16 text-gray-900">{course.title}</h3>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusBadge.color}`}>
                                {statusBadge.text}
                              </span>
                            </div>

                            <p className="regular-14 text-gray-600 mb-2">المدرس: {course.instructor}</p>

                            <div className="flex md:gap-4 items-center  space-x-4 space-x-reverse mb-3">
                              <div className="flex flex-col md:flex-row items-center gap-1">
                                <FaBook className="w-4 h-4 text-gray-500" />
                                <span className="regular-12 text-gray-600">
                                  {course.completedLessons}/{course.totalLessons} درس
                                </span>
                              </div>
                              <div className="flex flex-col md:flex-row items-center gap-1">
                                <FaCalendarAlt className="w-4 h-4 text-gray-500" />
                                <span className="regular-12 text-gray-600">
                                  اشتراك: {formatDate(course.enrolledDate)}
                                </span>
                              </div>
                            </div>

                            {/* شريط التقدم */}
                            <div className="mb-2">
                              <div className="flexBetween mb-1">
                                <span className="regular-12 text-gray-600">التقدم</span>
                                <span className="regular-12 text-gray-900 font-medium">{course.progress}%</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(course.progress)}`}
                                  style={{ width: `${course.progress}%` }}
                                ></div>
                              </div>
                            </div>

                            <div className="flex space-x-2 gap-2 flex-col md:flex-row md:justify-end space-x-reverse">
                                {/* TODO : send to the specified video */}
                              <Link href={`/course/${course.id}`} className="bg-accent cursor-pointer text-white mr-0 px-4 py-2 rounded-md regular-12 hover:bg-opacity-90 transition-colors">
                                متابعة التعلم
                              </Link>
                              <Link href={`/course/${course.id}`} className="border cursor-pointer border-gray-300 text-gray-700 px-4 py-2 rounded-md regular-12 hover:bg-gray-50 transition-colors">
                                تفاصيل الكورس
                              </Link>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* زر عرض المزيد/عرض أقل */}
                  {userCourses.length > 3 && (
                    <div className="text-center mt-6">
                      {!showAllCourses ? (
                        <button
                          onClick={handleShowMore}
                          className="bg-secondary cursor-pointer text-white px-6 py-3 rounded-lg hover:bg-secondry transition-colors regular-14"
                        >
                          عرض المزيد ({userCourses.length - 3} كورس إضافي)
                        </button>
                      ) : (
                        <button
                          onClick={handleShowLess}
                          className="border border-secondary cursor-pointer text-secondary px-6 py-3 rounded-lg hover:bg-secondary hover:text-white transition-colors regular-14"
                        >
                          عرض أقل
                        </button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* إحصائيات سريعة */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div className="bg-white rounded-lg shadow-lg p-4 text-center">
                <FaBook className="w-8 h-8 text-accent mx-auto mb-2" />
                <h3 className="bold-18 text-gray-900">{userCourses.length}</h3>
                <p className="regular-14 text-gray-600">{getStatsLabels(user?.role).total}</p>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-4 text-center">
                <FaUserGraduate className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <h3 className="bold-18 text-gray-900">
                  {userCourses.filter(course => course.status === 'completed').length}
                </h3>
                <p className="regular-14 text-gray-600">{getStatsLabels(user?.role).completed}</p>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-4 text-center">
                <FaClock className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                <h3 className="bold-18 text-gray-900">
                  {userCourses.reduce((total, course) => total + course.completedLessons, 0)}
                </h3>
                <p className="regular-14 text-gray-600">{getStatsLabels(user?.role).lessons}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;