import React from 'react'
import { FaGraduationCap, FaPhone, FaEnvelope, FaMapMarkerAlt, FaFacebook, FaTwitter, FaInstagram, FaLinkedin, } from 'react-icons/fa';
import Link from 'next/link';

const Footer = () => {
  return (
          <footer className="bg-gray-900 text-white py-16">
        <div className="max-container padding-container">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div>
              <div className="flexCenter gap-2 mb-6">
                <FaGraduationCap className="w-8 h-8 text-accent" />
                <h3 className="bold-24">أكاديميتنا</h3>
              </div>
              <p className="regular-14 text-gray-300 mb-6">
                منصة التعليم الرقمي الرائدة في مصر، نقدم تعليماً عالي الجودة
                لجميع المراحل التعليمية بأحدث التقنيات.
              </p>
              <div className="flex gap-4">
                <a href="#" className="w-10 h-10 bg-accent rounded-full flexCenter hover:bg-opacity-80 transition-colors">
                  <FaFacebook className="w-5 h-5" />
                </a>
                <a href="#" className="w-10 h-10 bg-accent rounded-full flexCenter hover:bg-opacity-80 transition-colors">
                  <FaTwitter className="w-5 h-5" />
                </a>
                <a href="#" className="w-10 h-10 bg-accent rounded-full flexCenter hover:bg-opacity-80 transition-colors">
                  <FaInstagram className="w-5 h-5" />
                </a>
                <a href="#" className="w-10 h-10 bg-accent rounded-full flexCenter hover:bg-opacity-80 transition-colors">
                  <FaLinkedin className="w-5 h-5" />
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="bold-18 mb-6">روابط سريعة</h4>
              <ul className="space-y-3">
                <li>
                  <Link href="/courses" className="regular-14 text-gray-300 hover:text-accent transition-colors">
                    الكورسات
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="regular-14 text-gray-300 hover:text-accent transition-colors">
                    من نحن
                  </Link>
                </li>
                <li>
                  <Link href="/instructors" className="regular-14 text-gray-300 hover:text-accent transition-colors">
                    المدرسين
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="regular-14 text-gray-300 hover:text-accent transition-colors">
                    المدونة
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="regular-14 text-gray-300 hover:text-accent transition-colors">
                    اتصل بنا
                  </Link>
                </li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="bold-18 mb-6">الدعم</h4>
              <ul className="space-y-3">
                <li>
                  <Link href="/help" className="regular-14 text-gray-300 hover:text-accent transition-colors">
                    مركز المساعدة
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="regular-14 text-gray-300 hover:text-accent transition-colors">
                    الأسئلة الشائعة
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="regular-14 text-gray-300 hover:text-accent transition-colors">
                    سياسة الخصوصية
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="regular-14 text-gray-300 hover:text-accent transition-colors">
                    الشروط والأحكام
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="bold-18 mb-6">تواصل معنا</h4>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <FaPhone className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-300">الهاتف</p>
                    <p className="bold-14">+20 ************</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <FaEnvelope className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-300">البريد الإلكتروني</p>
                    <p className="bold-14"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <FaMapMarkerAlt className="w-5 h-5 text-accent" />
                  <div>
                    <p className="regular-14 text-gray-300">العنوان</p>
                    <p className="bold-14">القاهرة، مصر</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-gray-700 mt-12 pt-8">
            <div className="flexBetween flex-col md:flex-row gap-4">
              <p className="regular-14 text-gray-400">
                © 2025 أكاديميتنا. جميع الحقوق محفوظة.
              </p>
              <div className="flex gap-6">
                <Link href="/privacy" className="regular-14 text-gray-400 hover:text-accent transition-colors">
                  سياسة الخصوصية
                </Link>
                <Link href="/terms" className="regular-14 text-gray-400 hover:text-accent transition-colors">
                  الشروط والأحكام
                </Link>
                <Link href="/cookies" className="regular-14 text-gray-400 hover:text-accent transition-colors">
                  سياسة الكوكيز
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
  )
}

export default Footer